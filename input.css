@tailwind base;
@tailwind components;
@tailwind utilities;

/* Preline UI */
@import "./node_modules/preline/variants.css";

/* Custom Preline gradient utilities */
.bg-linear-to-tl {
  background-image: linear-gradient(to top left, var(--tw-gradient-stops));
}
.bg-linear-to-tr {
  background-image: linear-gradient(to top right, var(--tw-gradient-stops));
}
.bg-linear-to-bl {
  background-image: linear-gradient(to bottom left, var(--tw-gradient-stops));
}
.bg-linear-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.outline-hidden {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:outline-hidden:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

/* Adds pointer cursor to buttons */
@layer base {
  button:not(:disabled),
  [role="button"]:not(:disabled) {
    cursor: pointer;
  }
}

/* Defaults hover styles on all devices */
@custom-variant hover (&:hover);
