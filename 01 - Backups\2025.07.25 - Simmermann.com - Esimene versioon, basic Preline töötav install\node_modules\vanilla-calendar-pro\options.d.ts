import { Calendar } from './index';
import { DateAny, DateMode, DatesArr, Labels, Layouts, Locale, MonthsCount, Popups, PositionToInput, Range, Styles, ThemesDefault, TimeControl, ToggleSelected, TypesCalendar, WeekDayID, WeekDays } from './types';
export default class OptionsCalendar {
    type: TypesCalendar;
    inputMode: boolean;
    positionToInput: PositionToInput;
    firstWeekday: WeekDayID;
    monthsToSwitch: 1 | MonthsCount;
    themeAttrDetect: string;
    locale: Locale;
    dateToday: DateAny;
    dateMin: DateAny;
    dateMax: DateAny;
    displayDateMin: DateAny;
    displayDateMax: DateAny;
    displayDatesOutside: boolean;
    displayDisabledDates: boolean;
    displayMonthsCount: MonthsCount;
    disableDates: DatesArr;
    disableAllDates: boolean;
    disableDatesPast: boolean;
    disableDatesGaps: boolean;
    disableWeekdays: Range<7>[];
    disableToday: boolean;
    enableDates: DatesArr;
    enableEdgeDatesOnly: boolean;
    enableDateToggle: ToggleSelected;
    enableWeekNumbers: boolean;
    enableMonthChangeOnDayClick: boolean;
    enableJumpToSelectedDate: boolean;
    selectionDatesMode: false | DateMode;
    selectionMonthsMode: boolean | 'only-arrows';
    selectionYearsMode: boolean | 'only-arrows';
    selectionTimeMode: false | 12 | 24;
    selectedDates: DatesArr;
    selectedMonth: Range<12>;
    selectedYear: number;
    selectedHolidays: DatesArr;
    selectedWeekends: WeekDays<WeekDayID>;
    selectedTime: string;
    selectedTheme: ThemesDefault | string;
    timeMinHour: Range<24>;
    timeMaxHour: Range<24>;
    timeMinMinute: Range<60>;
    timeMaxMinute: Range<60>;
    timeControls: TimeControl;
    timeStepHour: number;
    timeStepMinute: number;
    sanitizerHTML: (dirtyHtml: string) => string;
    onClickDate: (self: Calendar, event: MouseEvent) => void;
    onClickWeekDay: (self: Calendar, day: number, dateEls: HTMLElement[], event: MouseEvent) => void;
    onClickWeekNumber: (self: Calendar, number: number, year: number, dateEls: HTMLElement[], event: MouseEvent) => void;
    onClickTitle: (self: Calendar, event: MouseEvent) => void;
    onClickMonth: (self: Calendar, event: MouseEvent) => void;
    onClickYear: (self: Calendar, event: MouseEvent) => void;
    onClickArrow: (self: Calendar, event: MouseEvent) => void;
    onChangeTime: (self: Calendar, event: Event, isError: boolean) => void;
    onChangeToInput: (self: Calendar, event: Event) => void;
    onCreateDateRangeTooltip: (self: Calendar, dateEl: HTMLElement, tooltipEl: HTMLElement, dateElBCR: DOMRect, mainElBCR: DOMRect) => string;
    onCreateDateEls: (self: Calendar, dateEl: HTMLElement) => void;
    onCreateMonthEls: (self: Calendar, monthEl: HTMLElement) => void;
    onCreateYearEls: (self: Calendar, yearEl: HTMLElement) => void;
    onInit: (self: Calendar) => void;
    onUpdate: (self: Calendar) => void;
    onDestroy: (self: Calendar) => void;
    onShow: (self: Calendar) => void;
    onHide: (self: Calendar) => void;
    popups: Popups;
    labels: Labels;
    layouts: Layouts;
    styles: Styles;
}
